import { Suspense, useEffect, useState } from 'react';
import { RouterProvider, createBrowserRouter, Navigate } from 'react-router-dom';

import AdminPage from '../AdminPage';

import LoadingClient from "@/components/LoadingClient";
import NotFoundPage from '@/containers/NotFoundPage';
import { MenuRoles } from '@/containers/types/global';
import useAuthStore from '@/store/globalStore';
import { PUBLIC_PATH } from '@/utils/common/constants';
import { initializeApiToken, verifyApiToken } from '@/utils/common/storage';
import { isValidRoute } from '@/utils/router/routeHelpers';
import { componentMap } from '@/utils/router/routers';

export default function RouteApp() {
  const [router, setRouter] = useState<ReturnType<typeof createBrowserRouter> | null>(null);

  // Initialize API token globally for all APIs
  useEffect(() => {
    initializeApiToken();

    // Verify token was set correctly
    setTimeout(() => {
      const isTokenValid = verifyApiToken();
      if (!isTokenValid) {
        // API token verification failed - APIs may not work correctly
      }
    }, 100);
  }, []);

  // Get menu data from global store
  const menuData = useAuthStore((state) => state.menu);

  // Create routes only when menuData changes
  useEffect(() => {
    const buildRouter = () => {
      try {
        // Map menu roles to route objects, filtering out invalid routes
        const routeList = (menuData?.menuRoles || [])
          .filter((route: MenuRoles) => isValidRoute(route))
          .map((route: MenuRoles) => {
            const Component = componentMap[route.component];
            return {
              path: route.url,
              element: Component ? (
                <Suspense>
                  <Component />
                </Suspense>
              ) : (
                <NotFoundPage />
              ),
            };
          });

        // Create router with all routes
        const appRouter = createBrowserRouter(
          [
            {
              path: '/',
              element: <AdminPage />,
              children: [
                {
                  index: true,
                  element: <Navigate to="kafka-monitor" replace />
                },
                ...routeList
              ],
            },
            {
              path: '*',
              element: <NotFoundPage />,
            },
          ],
          {
            basename: PUBLIC_PATH,
          },
        );

        setRouter(appRouter);
      } catch (error) {
        console.error('Error building router:', error);
      }
    };

    buildRouter();
  }, [menuData]);

  return router ? <RouterProvider router={router} /> : <LoadingClient isLoading={!router} />;
}
