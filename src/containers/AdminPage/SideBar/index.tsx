import { Menu, Tooltip } from 'antd';
import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';

import { findPathByUrl, MenuItemData, MenuItemProps, RenderedMenuItem } from './type';

import logo from '@/assets/images/logo1.png';
import bg1 from '@/assets/images/menu1.jpg';

function SideBar(props: MenuItemProps) {
  const { dataMenu, collapsed, pathNameLocation } = props;

  const renderMenu = (menu: MenuItemData) => {
    let itemMenu = {
      key: menu.id,
      icon: menu.iconCls ? <i className={`${menu.iconCls}`}></i> : '',
      label: menu.url ? (
        <Tooltip title={menu.name}>
          <NavLink to={menu.url} className="flex items-center">
            <span>{menu.name}</span>
          </NavLink>
        </Tooltip>
      ) : (
        <Tooltip title={menu.name}>
          <span>{menu.name}</span>
        </Tooltip>
      ),
    } as RenderedMenuItem;

    if (menu.children && menu.children.length) {
      itemMenu.children = menu.children.map((e) => renderMenu(e));
    }
    return itemMenu;
  };

  return (
    <>
      <div className="flex flex-col h-full">
        <div
          className="absolute left-0 bottom-0 h-full w-full bg-bottom bg-no-repeat opacity-70 z-1"
          style={{ backgroundImage: `url(${bg1})` }}
        />
        {/* Logo và tiêu đề */}
        <div className="content border-b border-stone-50">
          <div className="flex relative items-center">
            <img alt="logo" src={logo} className="mx-4 my-1 w-[50px] h-[50px]" />
            <div>
              {!collapsed && (
                <label
                  className={`text-stone-50 absolute top-1/2 transform -translate-y-1/2 text-lg font-bold transition-all duration-500 ease-in-out ${collapsed ? 'opacity-0 -translate-x-4' : 'opacity-100 translate-x-0'
                    }`}
                >
                  BỘ CÔNG AN
                </label>
              )}
            </div>
          </div>
        </div>

        {/* Menu với scroll */}
        <div className="flex-1 overflow-y-auto max-h-[calc(100vh-70px)]">
          <Menu
            className="menu-sider"
            mode="inline"
            defaultOpenKeys={findPathByUrl(dataMenu, 'ds_vuan66') || []}
            defaultSelectedKeys={findPathByUrl(dataMenu, 'ds_vuan66') || []}
            items={dataMenu.map((e) => renderMenu(e))}
          />
        </div>
      </div>
    </>
  );
}

export default SideBar;
