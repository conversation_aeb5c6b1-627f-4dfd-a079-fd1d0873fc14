import { SearchOutlined, ReloadOutlined, SendOutlined, CheckOutlined } from '@ant-design/icons';
import { Button, Flex, DatePickerProps } from 'antd';
import React, { useRef, useEffect } from 'react';
import { z } from 'zod';
import { useShallow } from 'zustand/react/shallow';

import { trangThaiDisplayOptions, loaiOptions, phanMemOptions } from './constants';

import { Form, FloatingSelectField, FloatingTextField, FloatingDatePickerField } from '@/components/form';
import { FormRef } from '@/components/form/Form';
import { useDashboardStore } from '@/pages/KafkaMonitor/Dashboard/useKafkaMonitorStore';
import { THEME_COLORS } from '@/utils/ui';

export const filterSchema = z.object({
    searchText: z.string().optional(),
    maUngDung: z.string().optional(),
    topic: z.string().optional(),
    trangThai: z.string().optional(),
    gui<PERSON>han: z.string().optional(),
    loaiYeuCau: z.string().optional(),
    // Ngày tạo date range
    ngayTaoFromDate: z.date().nullable().optional(),
    ngayTaoToDate: z.date().nullable().optional(),
    // Ngày nhận phản hồi date range
    ngayNhanPhanHoiFromDate: z.date().nullable().optional(),
    ngayNhanPhanHoiToDate: z.date().nullable().optional(),
}).refine((data) => {
    // Validate ngayTao date range
    if (data.ngayTaoFromDate && data.ngayTaoToDate) {
        return data.ngayTaoFromDate <= data.ngayTaoToDate;
    }
    return true;
}, {
    message: "Từ ngày tạo phải nhỏ hơn hoặc bằng đến ngày tạo",
    path: ["ngayTaoFromDate"],
}).refine((data) => {
    // Validate ngayNhanPhanHoi date range
    if (data.ngayNhanPhanHoiFromDate && data.ngayNhanPhanHoiToDate) {
        return data.ngayNhanPhanHoiFromDate <= data.ngayNhanPhanHoiToDate;
    }
    return true;
}, {
    message: "Từ ngày nhận phản hồi phải nhỏ hơn hoặc bằng đến ngày nhận phản hồi",
    path: ["ngayNhanPhanHoiFromDate"],
});

export type FilterValues = z.infer<typeof filterSchema>;

export const KafkaMonitorFilterForm: React.FC = () => {
    // Get state and actions from the store
    const {
        handleSubmit,
        handleReset,
        isFilterLoading,
        filterValues,
        handleBulkResend,
        handleBulkMarkAsProcessed,
        selectedRowKeys
    } = useDashboardStore(
        useShallow((state: any) => ({
            handleSubmit: state.handleSubmit,
            handleReset: state.handleReset,
            isFilterLoading: state.isFilterLoading,
            filterValues: state.filterValues,
            handleBulkResend: state.handleBulkResend,
            handleBulkMarkAsProcessed: state.handleBulkMarkAsProcessed,
            selectedRowKeys: state.selectedRowKeys
        }))
    );

    const formRef = useRef<FormRef>(null);

    // Sync form values when filterValues change (e.g., from chart clicks)
    useEffect(() => {
        if (formRef.current && filterValues) {
            // Update form fields with current filter values
            Object.keys(filterValues).forEach(key => {
                if (filterValues[key] !== undefined && filterValues[key] !== null) {
                    formRef.current?.setValue(key, filterValues[key]);
                }
            });
        }
    }, [filterValues]);

    const onSubmit = (data: FilterValues) => {
        // Create a copy of the data to avoid mutating the original
        const formattedData = { ...data };

        // Format ngayTao dates with 00:00:00 and 23:59:59 time if they exist
        if (formattedData.ngayTaoFromDate instanceof Date) {
            const date = new Date(formattedData.ngayTaoFromDate);
            date.setHours(0, 0, 0, 0); // Set time to 00:00:00
            formattedData.ngayTaoFromDate = date;
        }

        if (formattedData.ngayTaoToDate instanceof Date) {
            const date = new Date(formattedData.ngayTaoToDate);
            date.setHours(23, 59, 59, 999); // Set time to 23:59:59
            formattedData.ngayTaoToDate = date;
        }

        // Format ngayNhanPhanHoi dates with 00:00:00 and 23:59:59 time if they exist
        if (formattedData.ngayNhanPhanHoiFromDate instanceof Date) {
            const date = new Date(formattedData.ngayNhanPhanHoiFromDate);
            date.setHours(0, 0, 0, 0); // Set time to 00:00:00
            formattedData.ngayNhanPhanHoiFromDate = date;
        }

        if (formattedData.ngayNhanPhanHoiToDate instanceof Date) {
            const date = new Date(formattedData.ngayNhanPhanHoiToDate);
            date.setHours(23, 59, 59, 999); // Set time to 23:59:59
            formattedData.ngayNhanPhanHoiToDate = date;
        }

        handleSubmit(formattedData);
    };

    const onReset = () => {
        if (formRef.current) {
            formRef.current.reset();
        }
        handleReset();
    };

    const handleResendAll = () => {
        handleBulkResend();
    };

    const handleMarkAllProcessed = () => {
        handleBulkMarkAsProcessed();
    };
    
    // Configure date picker disabling future dates
    const disableFutureDates: DatePickerProps['disabledDate'] = (current) => {
        // Disable dates in the future
        return current && current.isAfter(new Date(), 'day');
    };

    return (
        <Form
            onSubmit={onSubmit}
            schema={filterSchema}
            ref={formRef}
            className="w-full"
            defaultValues={{
                maUngDung: "014", // Default to NVCB CS
                ...filterValues // Use current filter values as defaults
            }}
        >

            <Flex gap="middle" className="mb-4" wrap="wrap">
                <div style={{ flex: 1, minWidth: '150px' }}>
                    <FloatingSelectField name="maUngDung" floatingLabel="Mã ứng dụng" options={phanMemOptions} allowClear />
                </div>
                <div style={{ flex: 1, minWidth: '150px' }}>
                    <FloatingTextField name="topic" floatingLabel="Topic" />
                </div>
                <div style={{ flex: 1, minWidth: '150px' }}>
                    <FloatingSelectField name="trangThai" floatingLabel="Trạng thái" options={trangThaiDisplayOptions} allowClear />
                </div>
                <div style={{ flex: 1, minWidth: '150px' }}>
                    <FloatingSelectField name="guiNhan" floatingLabel="Gửi/Nhận" options={loaiOptions} allowClear />
                </div>
                <div style={{ flex: 2, minWidth: '250px' }}>
                    <FloatingTextField name="searchText" floatingLabel="Từ khoá" />
                </div>
            </Flex>
            {/* Date sections */}
            <Flex gap="middle" className="mb-4" wrap="wrap">
                {/* Ngày tạo date pickers */}
                <div style={{ flex: 1, minWidth: '150px' }}>
                    <FloatingDatePickerField
                        name="ngayTaoFromDate"
                        floatingLabel="Ngày tạo từ"
                        format="DD/MM/YYYY"
                        disabledDate={disableFutureDates}
                    />
                </div>
                <div style={{ flex: 1, minWidth: '150px' }}>
                    <FloatingDatePickerField
                        name="ngayTaoToDate"
                        floatingLabel="Ngày tạo đến"
                        format="DD/MM/YYYY"
                        disabledDate={disableFutureDates}
                    />
                </div>

                {/* Ngày nhận phản hồi date pickers */}
                <div style={{ flex: 1, minWidth: '150px' }}>
                    <FloatingDatePickerField
                        name="ngayNhanPhanHoiFromDate"
                        floatingLabel="Ngày phản hồi từ"
                        format="DD/MM/YYYY"
                        disabledDate={disableFutureDates}
                    />
                </div>
                <div style={{ flex: 1, minWidth: '150px' }}>
                    <FloatingDatePickerField
                        name="ngayNhanPhanHoiToDate"
                        floatingLabel="Ngày phản hồi đến"
                        format="DD/MM/YYYY"
                        disabledDate={disableFutureDates}
                    />
                </div>
            </Flex>
            <Flex justify="center" gap="middle" className="mt-4">
                <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    htmlType="submit"
                    disabled={isFilterLoading}
                    style={{
                        backgroundColor: THEME_COLORS.BASE_COLOR,
                        borderColor: THEME_COLORS.BASE_COLOR
                    }}
                >
                    Tìm kiếm
                </Button>
                <Button
                    icon={<ReloadOutlined />}
                    onClick={onReset}
                    disabled={isFilterLoading}
                >
                    Làm mới
                </Button>
                <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleResendAll}
                    disabled={isFilterLoading}
                    style={{
                        backgroundColor: THEME_COLORS.BASE_COLOR,
                        borderColor: THEME_COLORS.BASE_COLOR
                    }}
                >
                    Gửi lại {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
                </Button>
                <Button
                    type="primary"
                    icon={<CheckOutlined />}
                    onClick={handleMarkAllProcessed}
                    disabled={isFilterLoading}
                    style={{
                        backgroundColor: THEME_COLORS.BASE_COLOR,
                        borderColor: THEME_COLORS.BASE_COLOR
                    }}
                >
                    Đã xử lý {selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`}
                </Button>
            </Flex>
        </Form>
    );
};
