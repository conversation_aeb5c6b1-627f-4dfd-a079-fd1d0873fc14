import type { AxiosResponse } from 'axios';

import { BACKEND_URL_APP, BACKEND_URL_SSO } from '@/utils';
import baseApi from '@/utils/api/baseApi';


// GET
export const apiGetInfoUser = (url: string, params?: Record<string, any>): Promise<any> => {
  return new Promise((resolve, reject) => {
    baseApi
      .get(`${BACKEND_URL_APP}${url}`, { params })
      .then((res: AxiosResponse) => {
        resolve(res?.data);
      })
      .catch((err: Error) => {
        reject(err);
      });
  });
};
// GET
export const apiGetToken = (url: string, params?: Record<string, any>): Promise<any> => {
  return new Promise((resolve, reject) => {
    baseApi
      .get(`${BACKEND_URL_SSO}${url}`, { params })
      .then((res: AxiosResponse) => {
        resolve(res?.data);
      })
      .catch((err: Error) => {
        reject(err);
      });
  });
};
