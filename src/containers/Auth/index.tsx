import React, { memo, useCallback, useEffect, useState } from 'react';

import { apiGetInfoUser, apiGetToken } from './api';

import useAuthStore from '@/store/globalStore';
import { ACCESS_TOKEN, AUTHCODE, REFRESH_TOKEN, dataMenu, listRouters } from '@/utils';

function Auth(props: { children: React.ReactNode }) {
  const { children } = props;

  const [isAuthenticated, setIsAuthenticated] = useState(true);
  const updateUserInfoData = useAuthStore((state) => state.setUserInfo);
  const updateUserMenu = useAuthStore((state) => state.setMenu);

  const callAPI = useCallback(
    async (callback?: () => void) => {
      const responseInfo = await apiGetInfoUser('api/v1/account/get-me');
      const onLoadMenu = await apiGetInfoUser('api/v1/permission/fe', {
        appCode: '028',
      });

      updateUserInfoData(responseInfo?.data?.detail || {});

      const menuObj = onLoadMenu?.data || {};
      menuObj.menuRoles = listRouters;
      menuObj.menuByRole = dataMenu;

      updateUserMenu(menuObj || {});
      if (callback) {callback();}
    },
    [],
  );

  const getToken = useCallback(
    async (code: string, callback?: () => void) => {
      // Truyền authCode trực tiếp chứ không lấy lại từ localStorage
      const res = await apiGetToken('token', { authCode: code });

      if (!res) {return;}

      localStorage.setItem(ACCESS_TOKEN, `Bearer ${res.access_token}`);
      localStorage.setItem(REFRESH_TOKEN, res.refresh_token);
    },
    [callAPI],
  );

  useEffect(() => {
    const checkLogin = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const isLogin = () => {
        const token = localStorage.getItem(AUTHCODE) || '';
        if (!token) {
          // window.location.href = BASE_URL + '/login';
          return;
        }
        setIsAuthenticated(true);
      };

      const authCode = urlParams.get('authCode') || '';
      const menuObj = {
          menuRoles: {},
          menuByRole: {},
      };
      menuObj.menuRoles = listRouters;
      menuObj.menuByRole = dataMenu;

      updateUserMenu(menuObj || {});

      if (authCode) {
        await getToken(authCode, isLogin);
      } else {
        await callAPI(isLogin);
      }
    };

    checkLogin().catch(error => {
      console.error('Error during authentication:', error);
    });
  }, []);

  return <>{children}</>;
}
export default memo(Auth);
