import { SendOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Row, Col, Space, Table, Checkbox } from 'antd';
import dayjs from 'dayjs';
import { FC, useRef, useState, useEffect } from 'react';
import { z } from 'zod';

import {
    MessageMonitorModalFilterForm,
    ModalFilterFormData
} from './MessageMonitorModalFilterForm';

import { MessageMonitorApi, MessageMonitorItem } from '@/api/message-monitor';
import { Form, SelectField, TextAreaField, DatePickerField } from '@/components/form';
import { FormRef } from '@/components/form/Form';
import { useProducerOptions } from '@/hooks/useProducerOptions';
import { THEME_COLORS } from '@/utils/ui';



// Define the schema for bulk resend form (no validation)
export const bulkResendSchema = z.object({
    producerId: z.string().nullable(),
    description: z.string().nullable(),
    type: z.string().nullable(),
    fromDate: z.date().nullable(),
    toDate: z.date().nullable(),
    scheduleAt: z.date().nullable(),
});

export type BulkResendFormData = z.infer<typeof bulkResendSchema>;

// Modal filter form data type is imported from the modal filter form

interface MessageMonitorBulkResendModalProps {
    visible: boolean;
    onClose: () => void;
    onSubmit: (data: BulkResendFormData, messageIds?: string[]) => Promise<void>;
    loading?: boolean;
}

const MessageMonitorBulkResendModal: FC<MessageMonitorBulkResendModalProps> = ({
    visible,
    onClose,
    onSubmit,
    loading = false,
}) => {
    const formRef = useRef<FormRef>(null);
    const [isRangeMode, setIsRangeMode] = useState<boolean>(false); // Default to IDS mode (table)
    const [messageData, setMessageData] = useState<MessageMonitorItem[]>([]);
    const [selectedMessageIds, setSelectedMessageIds] = useState<string[]>([]);
    const [isLoadingMessages, setIsLoadingMessages] = useState(false);


    // Modal filter form handles its own state

    // Get producer options from API
    const { options: producerOptions, loading: producerLoading } = useProducerOptions();

    // Load messages when modal opens (default IDS mode) or when switching to IDS mode
    useEffect(() => {
        if (!isRangeMode && visible) {
            loadMessages();
        }
    }, [isRangeMode, visible]);

    // Helper function to format single date for API with time
    const formatSingleDate = (date: Date): string => {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    };

    const loadMessages = async (filters?: ModalFilterFormData) => {
        try {
            setIsLoadingMessages(true);

            // Transform filter values to API params (same as main page)
            const params: any = {};

            if (filters) {
                // Basic filters - only include non-null values
                if (filters.searchText !== null && filters.searchText !== '') {
                    params.searchText = filters.searchText;
                }
                if (filters.producerId !== null && filters.producerId !== '') {
                    params.producerId = filters.producerId;
                }
                if (filters.topic !== null && filters.topic !== '') {
                    params.topic = filters.topic;
                }
                if (filters.sentStatus !== null && filters.sentStatus !== '') {
                    params.sentStatus = Number(filters.sentStatus);
                }

                // Date filters - only include non-null values
                if (filters.createdTime !== null) {
                    params.createdTime = formatSingleDate(filters.createdTime);
                }
                if (filters.lastProcessedTime !== null) {
                    params.lastProcessedTime = formatSingleDate(filters.lastProcessedTime);
                }
            }

            const response = await MessageMonitorApi.getAll(params);
            if (response.success && response.data) {
                setMessageData(response.data.content || []);
            }
        } catch (error) {
            // Error loading messages
        } finally {
            setIsLoadingMessages(false);
        }
    };

    const handleFilterDataLoaded = (data: MessageMonitorItem[], total: number) => {
        setMessageData(data);
    };

    const handleFilterReset = () => {
        setMessageData([]);
    };

    const handleSubmit = async (data: BulkResendFormData) => {
        try {
            // Set the type based on checkbox state
            const formDataWithType = {
                ...data,
                type: isRangeMode ? 'RANGE' : 'IDS'
            };

            const messageIds = !isRangeMode ? selectedMessageIds : undefined;

            await onSubmit(formDataWithType, messageIds);
            formRef.current?.reset();
            setIsRangeMode(false); // Reset to default IDS mode
            setSelectedMessageIds([]);
            onClose();
        } catch (error) {
            // Error in bulk resend modal
        }
    };

    const handleCancel = () => {
        formRef.current?.reset();
        setIsRangeMode(false); // Reset to default IDS mode
        setSelectedMessageIds([]);
        onClose();
    };

    const handleRangeModeChange = (checked: boolean) => {
        setIsRangeMode(checked);
        setSelectedMessageIds([]);
    };

    return (
        <Drawer
            title={
                <div style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: '#ffffff',
                    margin: 0,
                    padding: 0,
                    display: 'block'
                }}>
                    Gửi lại bản tin
                </div>
            }
            placement="right"
            open={visible}
            onClose={handleCancel}
            width={1000}
            styles={{
                header: {
                    backgroundColor: '#1b524f',
                    borderBottom: '1px solid #1b524f',
                    color: '#ffffff',
                    padding: '16px 24px',
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                },
                body: { padding: '0' }
            }}
            closeIcon={
                <span style={{
                    color: '#ffffff',
                    fontSize: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '22px',
                    height: '22px',
                    position: 'relative',
                    zIndex: 10,
                    cursor: 'pointer'
                }}>
                    ✕
                </span>
            }
            className="custom-drawer"
        >
            <div style={{
                padding: '16px 8px 0 8px',
                height: '100%',
                display: 'flex',
                flexDirection: 'column'
            }}>
                <div style={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column'
                }}>
                    <div style={{
                        flex: 1,
                        overflowY: 'auto',
                        paddingBottom: '80px' // Space for buttons
                    }}>
                        <Form
                            onSubmit={handleSubmit}
                            schema={bulkResendSchema}
                            ref={formRef}
                            className="w-full"
                        >
                        {/* Form Fields Card */}
                    <Card
                        title="Thông tin gửi lại"
                        size="small"
                        style={{ marginBottom: 16 }}
                        styles={{ body: { padding: '12px' } }}
                    >
                        <Row gutter={[16, 8]}>
                            <Col span={12}>
                                <div style={{ fontSize: '13px', marginBottom: '8px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Phần mềm gửi</span>
                                </div>
                                <SelectField
                                    name="producerId"
                                    placeholder="Chọn phần mềm gửi"
                                    options={producerOptions}
                                    loading={producerLoading}
                                />
                            </Col>
                            <Col span={12}>
                                <div style={{ fontSize: '13px', marginBottom: '8px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Vào lúc</span>
                                </div>
                                <DatePickerField
                                    name="scheduleAt"
                                    placeholder="Chọn thời gian"
                                    format="DD/MM/YYYY HH:mm:ss"
                                    showTime={{
                                        format: 'HH:mm:ss',
                                        defaultValue: dayjs().startOf('day')
                                    }}
                                />
                            </Col>
                        </Row>

                        {/* Checkbox in its own row */}
                        <Row gutter={[16, 8]} style={{ marginTop: 16 }}>
                            <Col span={24}>
                                <Checkbox
                                    checked={isRangeMode}
                                    onChange={(e) => handleRangeModeChange(e.target.checked)}
                                >
                                    Theo khoảng thời gian
                                </Checkbox>
                            </Col>
                        </Row>

                        {/* Content field - full row with TextArea */}
                        <Row gutter={[16, 8]} style={{ marginTop: 16 }}>
                            <Col span={24}>
                                <div style={{ fontSize: '13px', marginBottom: '8px' }}>
                                    <span style={{ color: '#666', fontWeight: 500 }}>Nội dung</span>
                                </div>
                                <TextAreaField
                                    name="description"
                                    placeholder="Nhập nội dung"
                                    rows={3}
                                />
                            </Col>
                        </Row>
                    </Card>

                    {/* Conditional Content based on Range Mode */}
                    {isRangeMode ? (
                        <Card
                            title="Chọn khoảng thời gian"
                            size="small"
                            style={{ marginBottom: 16 }}
                            styles={{ body: { padding: '12px' } }}
                        >
                            <Row gutter={[16, 8]}>
                                <Col span={12}>
                                    <div style={{ fontSize: '13px', marginBottom: '8px' }}>
                                        <span style={{ color: '#666', fontWeight: 500 }}>Từ ngày</span>
                                    </div>
                                    <DatePickerField
                                        name="fromDate"
                                        placeholder="Chọn từ ngày"
                                        format="DD/MM/YYYY HH:mm:ss"
                                        showTime={{
                                            format: 'HH:mm:ss',
                                            defaultValue: dayjs().startOf('day')
                                        }}
                                    />
                                </Col>
                                <Col span={12}>
                                    <div style={{ fontSize: '13px', marginBottom: '8px' }}>
                                        <span style={{ color: '#666', fontWeight: 500 }}>Đến ngày</span>
                                    </div>
                                    <DatePickerField
                                        name="toDate"
                                        placeholder="Chọn đến ngày"
                                        format="DD/MM/YYYY HH:mm:ss"
                                        showTime={{
                                            format: 'HH:mm:ss',
                                            defaultValue: dayjs().endOf('day')
                                        }}
                                    />
                                </Col>
                            </Row>
                        </Card>
                    ) : (
                        <>
                            <Card
                                title="Lọc bản tin"
                                size="small"
                                style={{ marginBottom: 16 }}
                                styles={{ body: { padding: '12px' } }}
                            >
                                <MessageMonitorModalFilterForm
                                    onDataLoaded={handleFilterDataLoaded}
                                    onReset={handleFilterReset}
                                />
                            </Card>

                            <Card
                                title="Chọn bản tin"
                                size="small"
                                style={{ marginBottom: 16 }}
                                styles={{ body: { padding: '12px' } }}
                            >
                                <Table
                                rowKey="messageId"
                                dataSource={messageData}
                                loading={isLoadingMessages}
                                pagination={{
                                    pageSize: 10,
                                    showSizeChanger: false,
                                    showQuickJumper: false,
                                    showTotal: (total, range) =>
                                        `${range[0]}-${range[1]} của ${total} bản ghi`,
                                }}
                                rowSelection={{
                                    selectedRowKeys: selectedMessageIds,
                                    onChange: (selectedRowKeys) => {
                                        setSelectedMessageIds(selectedRowKeys as string[]);
                                    },
                                }}
                                columns={[
                                    {
                                        title: 'Mã bản tin',
                                        dataIndex: 'messageId',
                                        key: 'messageId',
                                        width: 200,
                                        render: (text) => (
                                            <span style={{
                                                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                                fontSize: '11px'
                                            }}>
                                                {text}
                                            </span>
                                        ),
                                    },
                                    {
                                        title: 'Phần mềm gửi',
                                        dataIndex: 'producerId',
                                        key: 'producerId',
                                        width: 200,
                                    },
                                    {
                                        title: 'Ngày tạo',
                                        dataIndex: 'createdTime',
                                        key: 'createdTime',
                                        width: 200,
                                    },
                                ]}
                                scroll={{ y: 300 }}
                                size="small"
                            />
                        </Card>
                        </>
                    )}
                        </Form>
                    </div>

                    {/* Action Buttons - Fixed at bottom */}
                    <div style={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        backgroundColor: 'white',
                        padding: '16px',
                        borderTop: '1px solid #f0f0f0',
                        zIndex: 10
                    }}>
                        <Space style={{ width: '100%', justifyContent: 'center' }}>
                            <Button
                                type="primary"
                                icon={<SendOutlined />}
                                onClick={() => {
                                    // Get form values and submit
                                    const formValues = formRef.current?.getValues();
                                    if (formValues) {
                                        handleSubmit(formValues);
                                    }
                                }}
                                loading={loading}
                                style={{
                                    backgroundColor: THEME_COLORS.BASE_COLOR,
                                    borderColor: THEME_COLORS.BASE_COLOR
                                }}
                            >
                                Gửi lại
                            </Button>
                            <Button
                                onClick={handleCancel}
                                type="default"
                                disabled={loading}
                            >
                                Hủy
                            </Button>
                        </Space>
                    </div>
                </div>
            </div>
        </Drawer>
    );
};

export default MessageMonitorBulkResendModal;
