import React from 'react';

import { getMessageStatusDisplay, getMessageStatusColor, getMessageStatusBgColor } from './constants';

interface StatusTagProps {
  status: number;
  style?: React.CSSProperties;
}

export const StatusTag: React.FC<StatusTagProps> = ({ status, style = {} }) => {
  const statusText = getMessageStatusDisplay(status);
  const textColor = getMessageStatusColor(status);
  const backgroundColor = getMessageStatusBgColor(status);

  return (
    <span
      style={{
        display: 'inline-block',
        padding: '2px 8px',
        borderRadius: '6px',
        fontSize: '12px',
        fontWeight: 500,
        color: textColor,
        backgroundColor: backgroundColor,
        border: `1px solid ${textColor}`,
        lineHeight: '1.4',
        ...style
      }}
    >
      {statusText}
    </span>
  );
};

export default StatusTag;
