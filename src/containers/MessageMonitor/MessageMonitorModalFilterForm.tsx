import React, { useRef, useEffect } from 'react';
import { <PERSON><PERSON>, Flex } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { THEME_COLORS } from '@/utils/ui';
import { z } from 'zod';
import { FormRef } from '@/components/form/Form';
import { Form, FloatingSelectField, FloatingTextField, FloatingDatePickerField } from '@/components/form';
import dayjs from 'dayjs';
import { useProducerOptions } from '@/hooks/useProducerOptions';
import { messageSentStatusOptions } from './constants';
import { MessageMonitorApi, MessageMonitorItem } from '@/api/message-monitor';

// Modal filter form schema - no validation
const modalFilterSchema = z.object({
    searchText: z.string().nullable(),
    producerId: z.string().nullable(),
    topic: z.string().nullable(),
    sentStatus: z.string().nullable(),
    createdTime: z.date().nullable(),
    lastProcessedTime: z.date().nullable(),
});

export type ModalFilterFormData = z.infer<typeof modalFilterSchema>;

interface MessageMonitorModalFilterFormProps {
    onDataLoaded: (data: MessageMonitorItem[], total: number) => void;
    onReset: () => void;
}

export const MessageMonitorModalFilterForm: React.FC<MessageMonitorModalFilterFormProps> = ({
    onDataLoaded,
    onReset
}) => {
    const formRef = useRef<FormRef>(null);
    const { options: producerOptions, loading: producerLoading } = useProducerOptions();

    // Load initial data on mount
    useEffect(() => {
        handleSearch({
            searchText: null,
            producerId: null,
            topic: null,
            sentStatus: null,
            createdTime: null,
            lastProcessedTime: null,
        });
    }, []);

    const transformFilterToApiParams = (filterData: ModalFilterFormData) => {
        const params: any = {
            page: 1,
            size: 10,
        };

        if (filterData.searchText) {
            params.searchText = filterData.searchText;
        }
        if (filterData.producerId) {
            params.producerId = filterData.producerId;
        }
        if (filterData.topic) {
            params.topic = filterData.topic;
        }
        if (filterData.sentStatus) {
            params.sentStatus = filterData.sentStatus;
        }
        if (filterData.createdTime) {
            params.createdTimeFrom = dayjs(filterData.createdTime).format('YYYY-MM-DD HH:mm:ss');
        }
        if (filterData.lastProcessedTime) {
            params.lastProcessedTimeTo = dayjs(filterData.lastProcessedTime).format('YYYY-MM-DD HH:mm:ss');
        }

        return params;
    };

    const handleSearch = async (filterData: ModalFilterFormData) => {
        try {
            const apiParams = transformFilterToApiParams(filterData);

            const response = await MessageMonitorApi.getAll(apiParams);

            if (response.success && response.data) {
                onDataLoaded(response.data.content || [], response.data.totalElements || 0);
            }
        } catch (error) {
            // Modal search error
        }
    };

    const handleSubmit = (data: ModalFilterFormData) => {
        handleSearch(data);
    };

    const handleReset = () => {
        console.log('Modal form reset');
        formRef.current?.reset();
        const resetData = {
            searchText: null,
            producerId: null,
            topic: null,
            sentStatus: null,
            createdTime: null,
            lastProcessedTime: null,
        };
        handleSearch(resetData);
        onReset();
    };

    const handleManualSearch = () => {
        const formValues = formRef.current?.getValues();
        console.log('Modal manual search with values:', formValues);
        if (formValues) {
            handleSearch(formValues);
        }
    };

    const disableFutureDates = (current: any) => {
        return current && current > dayjs().endOf('day');
    };

    return (
        <div>
            <Form
                ref={formRef}
                onSubmit={handleSubmit}
                schema={modalFilterSchema}
                defaultValues={{
                    searchText: null,
                    producerId: null,
                    topic: null,
                    sentStatus: null,
                    createdTime: null,
                    lastProcessedTime: null,
                }}
            >
                {/* Filter fields */}
                <Flex gap="middle" className="mb-4" wrap="wrap">
                    <div style={{ flex: 1, minWidth: '150px' }}>
                        <FloatingSelectField
                            name="producerId"
                            floatingLabel="Phần mềm gửi"
                            options={producerOptions}
                            allowClear
                            loading={producerLoading}
                        />
                    </div>
                    <div style={{ flex: 1, minWidth: '150px' }}>
                        <FloatingTextField
                            name="topic"
                            floatingLabel="Topic"
                        />
                    </div>
                    <div style={{ flex: 1, minWidth: '150px' }}>
                        <FloatingSelectField
                            name="sentStatus"
                            floatingLabel="Trạng thái gửi"
                            options={messageSentStatusOptions}
                            allowClear
                        />
                    </div>
                    <div style={{ flex: 2, minWidth: '250px' }}>
                        <FloatingTextField
                            name="searchText"
                            floatingLabel="Từ khoá"
                        />
                    </div>
                </Flex>

                {/* Date sections */}
                <Flex gap="middle" className="mb-4" wrap="wrap">
                    <div style={{ flex: 1, minWidth: '200px' }}>
                        <FloatingDatePickerField
                            name="createdTime"
                            floatingLabel="Chọn ngày tạo"
                            format="DD/MM/YYYY HH:mm:ss"
                            showTime={{
                                format: 'HH:mm:ss',
                                defaultValue: dayjs().startOf('day')
                            }}
                            disabledDate={disableFutureDates}
                        />
                    </div>
                    <div style={{ flex: 1, minWidth: '200px' }}>
                        <FloatingDatePickerField
                            name="lastProcessedTime"
                            floatingLabel="Chọn ngày xử lý cuối"
                            format="DD/MM/YYYY HH:mm:ss"
                            showTime={{
                                format: 'HH:mm:ss',
                                defaultValue: dayjs().startOf('day')
                            }}
                            disabledDate={disableFutureDates}
                        />
                    </div>
                </Flex>
            </Form>

            <Flex justify="center" gap="middle" className="mt-4">
                <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleManualSearch}
                    style={{
                        backgroundColor: THEME_COLORS.BASE_COLOR,
                        borderColor: THEME_COLORS.BASE_COLOR
                    }}
                >
                    Tìm kiếm
                </Button>
                <Button
                    icon={<ReloadOutlined />}
                    onClick={handleReset}
                >
                    Làm mới
                </Button>
            </Flex>
        </div>
    );
};
