import { Select } from "antd";
import { useEffect, useState } from "react";

import { comboboxProps } from "./type";

import { useGetCommon } from "@/hooks/useGetCommon";


function ComboboxField(props: comboboxProps) {

    const {
        options,
        className,
        clearable,
        onChange,
        value,
        labelValue,
        onBlur,
        onSelect,
        margin,
        style,
        label,
        displayField,
        valueField,
        multiSelect,
        isDanhMuc,
        url,
        objParams,
        nameStorage = '',
        listeners,
        formRef,
        ...rest
    } = props;

    const [storeConverted, setStoreConverted] = useState<{ [key: string]: string }[]>([]);
    const [valueSel, setValueSel] = useState(value)

    const { isLoading, data } = useGetCommon({
        type: nameStorage
    })



    useEffect(() => {
        !isDanhMuc && options && Array.isArray(options) && setStoreConverted(options)
    }, [options]);

    useEffect(() => {
        if (isDanhMuc) {

        }
    }, [])


    return <Select
        value={valueSel}
        loading={isLoading}
        options={storeConverted}
        fieldNames={{ label: displayField, value: valueField }}
        style={{
            flex: 1,
            minWidth: 120,
            width: '100%',
            ...style
        }}
        {...rest}
    >

    </Select>


}

export default ComboboxField