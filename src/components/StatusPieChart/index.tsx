import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { <PERSON>, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import { MessageMonitorApi, MessageStatisticsRequest } from '@/api/message-monitor';
import KafkaMonitorApi from '@/api/kafka-monitor';
import { THEME_COLORS } from '@/utils/ui';
import dayjs from 'dayjs';

// Define colors for different status based on semantic meaning
const getStatusColor = (statusName: string): string => {
  const name = statusName.toLowerCase();

  // Success-related statuses - Green variants
  if (name.includes('thành công') || name.includes('successful')) {
    return '#16A34A'; // Green for success
  }

  // Failed statuses - Red variants
  if (name.includes('thất bại') || name.includes('failed')) {
    return '#EF4444'; // Red for failed
  }

  // Processing statuses - Blue variants
  if (name.includes('đang xử lý') || name.includes('processing')) {
    return '#3B82F6'; // Blue for processing
  }

  // Pending statuses - Yellow variants
  if (name.includes('chờ xử lý') || name.includes('pending')) {
    return '#F59E0B'; // Yellow for pending
  }

  // Fallback colors
  const fallbackColors = ['#8B5CF6', '#10B981', '#6366F1', '#EC4899'];
  const hash = name.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
  return fallbackColors[hash % fallbackColors.length];
};

interface StatusPieChartProps {
  applicationId?: string;
  filterParams?: any;
  onStatusClick?: (status: number) => void;
  apiType?: 'message' | 'kafka'; // Determine which API to use
}

interface ChartDataItem {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

export interface StatusPieChartRef {
  fetchData: () => void;
}

const StatusPieChart = forwardRef<StatusPieChartRef, StatusPieChartProps>(({
  applicationId,
  filterParams,
  onStatusClick,
  apiType = 'message' // Default to message API for backward compatibility
}, ref) => {
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState<ChartDataItem[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Expose fetchData function to parent component
  useImperativeHandle(ref, () => ({
    fetchData
  }));

  // Transform filter params to match MessageStatisticsRequest API format
  const transformFilterParams = (filterValues: any): MessageStatisticsRequest => {
    const params: MessageStatisticsRequest = {};

    if (filterValues) {
      // Map filter params to MessageStatisticsRequest format
      if (filterValues.appCode) params.appCode = filterValues.appCode;
      if (filterValues.topic) params.topic = filterValues.topic;
      if (filterValues.sentStatus) params.sentStatus = filterValues.sentStatus;
      if (filterValues.fromDate) params.fromDate = filterValues.fromDate;
      if (filterValues.toDate) params.toDate = filterValues.toDate;
      if (filterValues.interval) params.interval = filterValues.interval;
    }

    return params;
  };

  // Fetch data from appropriate API based on apiType
  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      if (apiType === 'kafka') {
        // Use Kafka Monitor API
        const appId = applicationId || '014';

        const response = await KafkaMonitorApi.getStatusCount(appId, filterParams);

        if (response.success && response.data) {
          // Transform Kafka API response to chart data
          const chartItems: ChartDataItem[] = [];

          response.data.forEach(item => {
            let statusName = '';
            let statusColor = '';

            switch (item.trangThai) {
              case 1:
                statusName = 'Thành công';
                statusColor = getStatusColor('thành công');
                break;
              case 0:
                statusName = 'Chờ xử lý';
                statusColor = getStatusColor('chờ xử lý');
                break;
              case -1:
                statusName = 'Thất bại';
                statusColor = getStatusColor('thất bại');
                break;
              case 2:
                statusName = 'Đang xử lý';
                statusColor = getStatusColor('đang xử lý');
                break;
              default:
                statusName = `Trạng thái ${item.trangThai}`;
                statusColor = getStatusColor('khác');
            }

            if (item.count > 0) {
              chartItems.push({
                name: statusName,
                value: item.count,
                percentage: 0, // Will calculate below
                color: statusColor
              });
            }
          });

          // Calculate percentages
          const total = chartItems.reduce((sum, item) => sum + item.value, 0);
          chartItems.forEach(item => {
            item.percentage = total > 0 ? Math.round((item.value / total) * 100) : 0;
          });

          // Sort by value descending
          chartItems.sort((a, b) => b.value - a.value);

          setChartData(chartItems);
        } else {
          setChartData([]);
          if (!response.success) {
            setError(response.message || 'Không thể tải dữ liệu');
          }
        }
      } else {
        // Use Message Monitor API (original logic)
        const transformedParams = transformFilterParams(filterParams);

        const response = await MessageMonitorApi.getStatistics(transformedParams);

        if (response.success && response.data) {
        const { sentStats } = response.data;
        
        if (sentStats) {
          // Create chart data from sentStats
          const chartItems: ChartDataItem[] = [];
          
          if (sentStats.successful > 0) {
            chartItems.push({
              name: 'Gửi thành công',
              value: sentStats.successful,
              percentage: 0, // Will calculate below
              color: getStatusColor('thành công')
            });
          }
          
          if (sentStats.failed > 0) {
            chartItems.push({
              name: 'Gửi thất bại',
              value: sentStats.failed,
              percentage: 0, // Will calculate below
              color: getStatusColor('thất bại')
            });
          }
          
          if (sentStats.processing > 0) {
            chartItems.push({
              name: 'Đang xử lý',
              value: sentStats.processing,
              percentage: 0, // Will calculate below
              color: getStatusColor('đang xử lý')
            });
          }
          
          if (sentStats.pending > 0) {
            chartItems.push({
              name: 'Chờ xử lý',
              value: sentStats.pending,
              percentage: 0, // Will calculate below
              color: getStatusColor('chờ xử lý')
            });
          }

          // Calculate percentages
          const total = sentStats.total || chartItems.reduce((sum, item) => sum + item.value, 0);
          chartItems.forEach(item => {
            item.percentage = total > 0 ? Math.round((item.value / total) * 100) : 0;
          });

          // Sort by value descending
          chartItems.sort((a, b) => b.value - a.value);

          setChartData(chartItems);
        } else {
          setChartData([]);
        }
      } else {
        setChartData([]);
        if (!response.success) {
          setError(response.message || 'Không thể tải dữ liệu');
        }
        }
      }
    } catch (err) {
      console.error('Error fetching message statistics:', err);
      setError('Lỗi khi tải dữ liệu thống kê');
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when dependencies change
  useEffect(() => {
    fetchData();
  }, [applicationId, filterParams]);

  // Also fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Handle pie segment click
  const handlePieClick = (data: ChartDataItem) => {
    if (onStatusClick) {
      // Map status names to status codes
      let statusCode = 1; // default to success
      if (data.name.includes('thất bại')) statusCode = -1;
      else if (data.name.includes('đang xử lý')) statusCode = 2;
      else if (data.name.includes('chờ xử lý')) statusCode = 0;
      
      onStatusClick(statusCode);
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: 'white',
          padding: '8px 12px',
          border: '1px solid #ccc',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>{data.name}</p>
          <p style={{ margin: 0, color: data.color }}>
            Số lượng: {data.value} ({data.percentage}%)
          </p>
        </div>
      );
    }
    return null;
  };

  if (error) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '320px',
        color: '#ff4d4f',
        textAlign: 'center'
      }}>
        <div style={{ marginBottom: '16px' }}>{error}</div>
        <Button onClick={fetchData} loading={loading}>
          Thử lại
        </Button>
      </div>
    );
  }

  return (
    <Spin spinning={loading}>
      <div style={{
        display: 'flex',
        height: '380px',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        {/* Chart Section */}
        <div style={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          borderRight: '1px solid #e5e7eb',
          borderRadius: '0 8px 8px 0',
          paddingRight: '16px'
        }}>
          {chartData.length > 0 ? (
            <div style={{
              width: '350px',
              height: '350px'
            }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    outerRadius={150}
                    innerRadius={0}
                    fill="#8884d8"
                    dataKey="value"
                    onClick={handlePieClick}
                    style={{ cursor: 'pointer' }}
                    stroke="none"
                    strokeWidth={0}
                  >
                    {chartData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.color}
                        style={{
                          filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                        }}
                      />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              color: '#999'
            }}>
              {loading ? 'Đang tải...' : 'Không có dữ liệu'}
            </div>
          )}
        </div>

        {/* Legend Section */}
        <div style={{
          flex: 1,
          padding: '16px 16px 16px 24px',
          overflow: 'auto',
          borderRadius: '8px 0 0 8px'
        }}>
          {chartData.length > 0 ? (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px'
            }}>
              {chartData.map((data, index) => (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    transition: 'all 0.2s ease',
                    border: '1px solid transparent'
                  }}
                  onClick={() => handlePieClick(data)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8f9fa';
                    e.currentTarget.style.borderColor = '#e9ecef';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.borderColor = 'transparent';
                  }}
                >
                  <div
                    style={{
                      width: '14px',
                      height: '14px',
                      backgroundColor: data.color,
                      marginRight: '10px',
                      borderRadius: '3px',
                      flexShrink: 0
                    }}
                  />
                  <span style={{
                    fontSize: '14px',
                    color: '#333',
                    fontWeight: '500',
                    wordBreak: 'break-word'
                  }}>
                    {data.name}: {data.value} ({data.percentage}%)
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              color: '#999'
            }}>
              Không có dữ liệu
            </div>
          )}
        </div>
      </div>
    </Spin>
  );
});

StatusPieChart.displayName = 'StatusPieChart';

export default StatusPieChart;
