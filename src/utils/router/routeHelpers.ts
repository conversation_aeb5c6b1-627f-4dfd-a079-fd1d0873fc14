import { MenuRoles } from "@/containers/types/global";
import { componentMap } from "@/utils";


/**
 * Check if a route component exists in the component map
 * @param componentName Component name to check
 * @returns boolean indicating if component exists
 */
export const componentExists = (componentName: string): boolean => {
  return !!componentMap[componentName];
};

/**
 * Validate route configuration
 * @param route Route object to validate
 * @returns boolean indicating if route is valid
 */
export const isValidRoute = (route: MenuRoles): boolean => {
  return (
    !!route.component &&
    !!route.url &&
    componentExists(route.component)
  );
};

/**
 * Build a complete URL path with parent paths
 * @param route Current route
 * @param parentPath Optional parent path
 * @returns Complete URL path
 */
export const buildRoutePath = (route: MenuRoles, parentPath: string = ''): string => {
  const basePath = parentPath ? `${parentPath}/` : '';
  return `${basePath}${route.url}`;
};
