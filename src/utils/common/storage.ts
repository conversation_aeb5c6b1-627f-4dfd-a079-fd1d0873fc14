/**
 * Type-safe wrapper for localStorage
 */

import { STORAGE_KEYS } from '@/utils';

/**
 * Get a value from localStorage with type safety
 * @param key Storage key
 * @param defaultValue Default value to return if key doesn't exist
 * @returns Parsed value or default value
 */
export function getStorageItem<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    return item ? (JSON.parse(item) as T) : defaultValue;
  } catch (error) {
    console.error(`Error getting item ${key} from localStorage:`, error);
    return defaultValue;
  }
}

/**
 * Set a value in localStorage with type safety
 * @param key Storage key
 * @param value Value to store
 */
export function setStorageItem<T>(key: string, value: T): void {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error setting item ${key} in localStorage:`, error);
  }
}

/**
 * Remove a value from localStorage
 * @param key Storage key
 */
export function removeStorageItem(key: string): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing item ${key} from localStorage:`, error);
  }
}

/**
 * Clear all items from localStorage
 */
export function clearStorage(): void {
  try {
    localStorage.clear();
  } catch (error) {
    console.error('Error clearing localStorage:', error);
  }
}

/**
 * Get user settings from localStorage
 * @returns User settings object or default empty object
 */
export function getUserSettings<T extends object>(): T {
  return getStorageItem<T>(STORAGE_KEYS.USER_SETTINGS, {} as T);
}

/**
 * Save user settings to localStorage
 * @param settings Settings object to save
 */
export function saveUserSettings<T extends object>(settings: T): void {
  setStorageItem(STORAGE_KEYS.USER_SETTINGS, settings);
}

/**
 * Get current theme from localStorage
 * @returns Theme value or default 'light'
 */
export function getTheme(): string {
  return getStorageItem<string>(STORAGE_KEYS.THEME, 'light');
}

/**
 * Save theme to localStorage
 * @param theme Theme value to save
 */
export function saveTheme(theme: string): void {
  setStorageItem(STORAGE_KEYS.THEME, theme);
}

/**
 * Get authentication token from localStorage
 * @returns Token value or null
 */
export function getAuthToken(): string | null {
  try {
    return localStorage.getItem('access_token');
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
}

/**
 * Save authentication token to localStorage
 * @param token Token value to save
 */
export function saveAuthToken(token: string): void {
  try {
    localStorage.setItem('access_token', token);
  } catch (error) {
    console.error('Error saving auth token:', error);
  }
}

/**
 * Initialize API token based on configuration
 * Sets the hardcoded access token for ALL API calls
 */
export function initializeApiToken(): void {
  try {
    // Import here to avoid circular dependency
    const { API_ACCESS_TOKEN } = require('@/utils/common/constants');

    if (API_ACCESS_TOKEN) {
      const bearerToken = `Bearer ${API_ACCESS_TOKEN}`;

      // Always set the token to ensure all APIs use it
      saveAuthToken(bearerToken);

      // Debug: Verify token was saved
      const savedToken = localStorage.getItem('access_token');

      // Force token to be available immediately
      if (!savedToken || savedToken !== bearerToken) {
        localStorage.setItem('access_token', bearerToken);
      }
    }
  } catch {
    // Error initializing API token
  }
}

/**
 * Verify that the access token is properly set and available for API calls
 * @returns boolean indicating if token is available
 */
export function verifyApiToken(): boolean {
  try {
    const token = localStorage.getItem('access_token');
    const isValid = !!token && token.startsWith('Bearer ');

    return isValid;
  } catch {
    return false;
  }
}

/**
 * @deprecated Use initializeApiToken instead
 * Kept for backward compatibility
 */
export function initializeMessageMonitorToken(): void {
  initializeApiToken();
}
