import { notification } from 'antd';
import { AxiosError } from 'axios';

import { ApiResponse } from '@/utils';

/**
 * Type guard to check if an error is an AxiosError
 */
export function isAxiosError(error: any): error is AxiosError {
  return error && error.isAxiosError === true;
}

/**
 * Handle API errors in a consistent way
 * @param error The error object
 * @param fallbackMessage Optional fallback message if error doesn't contain a message
 */
export function handleApiError(error: unknown, fallbackMessage: string = 'An unexpected error occurred'): void {
  if (isAxiosError(error)) {
    const status = error.response?.status;
    const responseData = error.response?.data as ApiResponse | undefined;

    // Get error message from response if available
    const errorMessage = responseData?.message || error.message || fallbackMessage;

    // Show different notifications based on error status
    if (status === 401 || status === 403) {
      notification.error({
        message: 'Authentication Error',
        description: errorMessage,
      });
    } else if (status && status >= 500) {
      notification.error({
        message: 'Server Error',
        description: errorMessage,
      });
    } else if (status && status >= 400) {
      notification.warning({
        message: 'Request Error',
        description: errorMessage,
      });
    } else {
      notification.error({
        message: 'Network Error',
        description: errorMessage,
      });
    }
  } else if (error instanceof Error) {
    // Handle non-Axios errors
    notification.error({
      message: 'Error',
      description: error.message || fallbackMessage,
    });
  } else {
    // Handle unknown errors
    notification.error({
      message: 'Error',
      description: fallbackMessage,
    });
  }
}

/**
 * Parse API response data safely
 * @param response API response object
 * @param defaultValue Default value to return if parsing fails
 */
export function parseApiResponse<T>(response: ApiResponse<T> | null | undefined, defaultValue: T): T {
  if (!response) {return defaultValue;}
  
  try {
    return response.data || defaultValue;
  } catch {
    return defaultValue;
  }
}

// API configuration helpers
export const getApiBaseUrl = (): string => {
  // Get base URL from environment or use default
  // Fix: Check if process exists before accessing process.env
  let baseUrl = 'https://bono.micro-tech.com.vn/api/v2/monitor';

  try {
    if (typeof process !== 'undefined' && process.env && process.env.REACT_APP_BACKEND_URL_EXTERNAL_SERVICE) {
      baseUrl = process.env.REACT_APP_BACKEND_URL_EXTERNAL_SERVICE;
    }
  } catch {
    // Could not access process.env, using default API base URL
  }

  return baseUrl;
};

export const getApiHeaders = (): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add authorization token if available - use correct key
  const token = localStorage.getItem('access_token'); // Changed from 'ACCESS_TOKEN' to 'access_token'
  if (token) {
    headers.Authorization = token; // Token already includes 'Bearer ' prefix
  }

  return headers;
};

// Export common API utilities
export * from './baseApi';
