import type { AxiosRequestConfig, AxiosResponse } from 'axios';

import baseApi from './baseApi';

import { handleApiError } from '@/utils/api/apiHelpers';

/**
 * Interface for API response
 */
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message: string;
  success: boolean;
}

/**
 * Generic GET request
 * @param url - API endpoint
 * @param config - Axios request configuration
 * @returns Promise with typed data response
 */
export const get = async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<ApiResponse<T>> = await baseApi.get(url, config);
    return response?.data?.data;
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};

/**
 * Generic POST request
 * @param url - API endpoint
 * @param data - Request payload
 * @param config - Axios request configuration
 * @returns Promise with typed data response
 */
export const post = async <T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<ApiResponse<T>> = await baseApi.post(url, data, config);
    return response?.data?.data;
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};

/**
 * Generic PUT request
 * @param url - API endpoint
 * @param data - Request payload
 * @param config - Axios request configuration
 * @returns Promise with typed data response
 */
export const put = async <T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<ApiResponse<T>> = await baseApi.put(url, data, config);
    return response?.data?.data;
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};

/**
 * Generic PATCH request for partial updates
 * @param url - API endpoint
 * @param data - Request payload (partial data)
 * @param config - Axios request configuration
 * @returns Promise with typed data response
 */
export const patch = async <T = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<ApiResponse<T>> = await baseApi.patch(url, data, config);
    return response?.data?.data;
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};

/**
 * Generic DELETE request
 * @param url - API endpoint
 * @param config - Axios request configuration
 * @returns Promise with typed data response
 */
export const del = async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<ApiResponse<T>> = await baseApi.delete(url, config);
    return response?.data?.data;
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};

/**
 * Upload file(s) using FormData
 * @param url - API endpoint
 * @param files - Files to upload (single file or array of files)
 * @param additionalData - Additional form data to include
 * @param config - Axios request configuration
 * @returns Promise with typed data response
 */
export const uploadFiles = async <T = any>(
  url: string,
  files: File | File[],
  additionalData?: Record<string, any>,
  config?: AxiosRequestConfig
): Promise<T> => {
  // Create FormData instance
  const formData = new FormData();

  // Append files
  if (Array.isArray(files)) {
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });
  } else {
    formData.append('file', files);
  }

  // Append additional data
  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (typeof value === 'object' && !(value instanceof File)) {
          formData.append(key, JSON.stringify(value));
        } else {
          // Cast value to string explicitly to avoid TypeScript error
          formData.append(key, value as string | Blob);
        }
      }
    });
  }

  // Custom config for file uploads
  const uploadConfig: AxiosRequestConfig = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    ...config,
  };

  return post<T>(url, formData, uploadConfig);
};

/**
 * Download a file from the server
 * @param endpoint - API endpoint
 * @param filename - Name to save the file as (required string)
 * @param config - Axios request configuration
 * @returns Promise that resolves when download is complete
 */
export const downloadFile = async (
  endpoint: string,
  filename: string,
  config?: AxiosRequestConfig
): Promise<void> => {
  try {
    const response = await baseApi.get(endpoint, {
      ...config,
      responseType: 'blob',
    });

    // Use provided filename or extract from Content-Disposition header
    let downloadFilename = filename;

    if (response.headers['content-disposition']) {
      const contentDisposition = response.headers['content-disposition'];
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        downloadFilename = filenameMatch[1].replace(/['"]/g, '');
        downloadFilename = decodeURIComponent(downloadFilename);
      }
    }

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', downloadFilename);
    document.body.appendChild(link);
    link.click();

    // Clean up
    setTimeout(() => {
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    }, 100);
  } catch (error) {
    handleApiError(error);
    throw error;
  }
};

// Export all request functions
const requests = {
  get,
  post,
  put,
  patch,
  delete: del,
  uploadFiles,
  downloadFile
};

export default requests;
