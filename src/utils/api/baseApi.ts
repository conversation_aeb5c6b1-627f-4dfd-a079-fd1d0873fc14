import type { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import axios from 'axios';

import { BASE_URL, handleError } from '@/utils/common';
import { ACCESS_TOKEN, REFRESH_TOKEN } from '@/utils/common';

// API response type
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message: string;
  success: boolean;
}

// Create axios instance with default config
const api = axios.create({
  // baseURL,
  timeout: 60,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Request interceptor to add authorization headers and other configs
 */
const onRequest = async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
  const newConfig = { ...config };

  // Set validation for status
  newConfig.validateStatus = (status: number) => status < 400;

  // Set content type if not already set
  if (!newConfig?.headers['Content-Type']) {
    newConfig.headers['Content-Type'] = 'application/json';
  }

  // Add authorization token if available
  const token = localStorage.getItem(ACCESS_TOKEN);

  if (token) {
    newConfig.headers.Authorization = token;
  }

  // Add client timestamp for request tracking
  newConfig.headers.clientTime = new Date().toISOString();

  return newConfig;
};

/**
 * Handle request errors
 */
const onRequestError = (error: AxiosError): Promise<AxiosError> => {
  handleError(error);
  return Promise.reject(error);
};

/**
 * Handle successful responses and check for auth issues
 */
const onResponse = (response: AxiosResponse): AxiosResponse => {
  // Handle unauthorized responses
  if (response.status === 401) {
    handleError(response);
    // Could implement redirect to login or token refresh here
  }
  return response;
};

/**
 * Handle response errors including auth issues, network errors, etc.
 */
const onResponseError = (error: AxiosError): Promise<AxiosError> => {
  const status = error.response?.status;

  // Handle authentication errors
  if (status === 401) {
    // Clear tokens on auth error
    localStorage.removeItem(ACCESS_TOKEN);
    localStorage.removeItem(REFRESH_TOKEN);

    // Redirect to login page or refresh token logic could be implemented here
    window.location.href = BASE_URL;
  }

  handleError(error);
  return Promise.reject(error);
};

/**
 * Set up all interceptors
 */
export function setupInterceptorsTo(axiosInstance: AxiosInstance): AxiosInstance {
  axiosInstance.interceptors.request.use(onRequest, onRequestError);
  axiosInstance.interceptors.response.use(onResponse, onResponseError);
  return axiosInstance;
}

// Export the configured API instance
export default setupInterceptorsTo(api);
