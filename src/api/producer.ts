import api from '../utils/api/baseApi';
import { ApiResponse } from '@/utils';
import { BACKEND_URL_EXTERNAL_SERVICE } from '@/utils/common/constants';

// Producer interface
export interface Producer {
  code: string;
  name: string;
}

/**
 * Producer API class for managing producer data
 */
export class ProducerApi {
  private static readonly BASE_URL = `${BACKEND_URL_EXTERNAL_SERVICE}/app`;

  /**
   * Get all producers
   * @returns Promise with list of producers
   */
  static async getAll(): Promise<ApiResponse<Producer[]>> {
    try {
      const response = await api.get<ApiResponse<Producer[]>>(this.BASE_URL);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}
