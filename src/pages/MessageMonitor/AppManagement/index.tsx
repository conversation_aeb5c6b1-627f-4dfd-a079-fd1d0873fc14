import { ReloadOutlined, SettingOutlined } from '@ant-design/icons';
import { Card, Table, Button, Tag, message, Modal } from 'antd';
import dayjs from 'dayjs';
import React, { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';

import { AppManagementApi, AppInfo, AppStatus, JobType } from '@/api/app-management';
import { FloatingSelectField } from '@/components/form/FloatingSelectField';
import { THEME_COLORS } from '@/utils/ui';

interface AppWithStatus extends AppInfo {
  status?: AppStatus;
}

const AppManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [apps, setApps] = useState<AppWithStatus[]>([]);
  const [resetModalVisible, setResetModalVisible] = useState(false);

  // Form setup for floating fields
  const methods = useForm({
    defaultValues: {
      appCode: '',
      jobType: 'ALL'
    }
  });

  // Fetch apps and their statuses
  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch apps and statuses in parallel
      const [appsResponse, statusesResponse] = await Promise.all([
        AppManagementApi.getAllApps(),
        AppManagementApi.getAllAppSyncStatuses()
      ]);

      if (appsResponse.success && statusesResponse.success) {
        const appsData = appsResponse.data || [];
        const statusesData = statusesResponse.data || [];

        // Combine apps with their statuses
        const appsWithStatus: AppWithStatus[] = appsData.map(app => ({
          ...app,
          status: statusesData.find(status => status.appCode === app.code)
        }));

        setApps(appsWithStatus);
      } else {
        message.error('Không thể tải dữ liệu ứng dụng');
      }
    } catch (error) {
      console.error('Error fetching app data:', error);
      message.error('Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Handle reset last runtime
  const handleReset = async () => {
    try {
      const formData = methods.getValues();

      if (!formData.appCode) {
        message.error('Vui lòng chọn ứng dụng');
        return;
      }

      setLoading(true);

      if (formData.jobType === 'ALL') {
        await AppManagementApi.resetAllLastRuntimeForApp(formData.appCode);
        message.success('Reset tất cả job types thành công');
      } else {
        await AppManagementApi.resetLastRuntime(formData.appCode, formData.jobType as JobType);
        message.success(`Reset job type ${formData.jobType} thành công`);
      }

      setResetModalVisible(false);
      methods.reset();

      // Refresh data
      await fetchData();
    } catch (error) {
      console.error('Error resetting runtime:', error);
      message.error('Có lỗi xảy ra khi reset');
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) {return '-';}
    return dayjs(dateString).format('DD/MM/YYYY HH:mm:ss');
  };

  // Get status color
  const getStatusColor = (lastTime?: string) => {
    if (!lastTime) {return 'default';}
    const now = dayjs();
    const last = dayjs(lastTime);
    const diffHours = now.diff(last, 'hour');
    
    if (diffHours < 1) {return 'success';}
    if (diffHours < 24) {return 'warning';}
    return 'error';
  };

  const columns = [
    {
      title: 'Mã ứng dụng',
      dataIndex: 'code',
      key: 'code',
      width: 100,
      align: 'center' as const,
    },
    {
      title: <div style={{ textAlign: 'center' }}>Tên ứng dụng</div>,
      dataIndex: 'name',
      key: 'name',
      width: 180,
      align: 'left' as const,
    },
    {
      title: <div style={{ textAlign: 'center' }}>Mô tả</div>,
      dataIndex: 'description',
      key: 'description',
      width: 240,
      align: 'left' as const,
      render: (text?: string) => text || '-',
    },
    {
      title: 'Lần gửi cuối',
      key: 'lastSentTime',
      width: 160,
      align: 'center' as const,
      render: (_: any, record: AppWithStatus) => (
        <div>
          <Tag color={getStatusColor(record.status?.lastSentTime)}>
            {formatDate(record.status?.lastSentTime)}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Lần đồng bộ cuối',
      key: 'lastSyncTime',
      width: 160,
      align: 'center' as const,
      render: (_: any, record: AppWithStatus) => (
        <div>
          <Tag color={getStatusColor(record.status?.lastSyncTime)}>
            {formatDate(record.status?.lastSyncTime)}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Đồng bộ tracking cuối',
      key: 'lastTrackingSyncTime',
      width: 160,
      align: 'center' as const,
      render: (_: any, record: AppWithStatus) => (
        <div>
          <Tag color={getStatusColor(record.status?.lastTrackingSyncTime)}>
            {formatDate(record.status?.lastTrackingSyncTime)}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 60,
      align: 'center' as const,
      render: (_: any, record: AppWithStatus) => (
        <Button
          type="link"
          icon={<SettingOutlined />}
          onClick={() => {
            methods.setValue('appCode', record.code);
            setResetModalVisible(true);
          }}
          style={{
            color: THEME_COLORS.BASE_COLOR,
          }}
        >
          Reset
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '16px' }}>
      <Card
        title="Quản lý ứng dụng"
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchData}
            loading={loading}
          >
            Làm mới
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={apps}
          rowKey="code"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} ứng dụng`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Reset Modal */}
      <Modal
        title="Reset Last Runtime"
        open={resetModalVisible}
        onCancel={() => {
          setResetModalVisible(false);
          methods.reset();
        }}
        width={700}
        footer={null} // Remove default footer since we'll add custom buttons
        centered // Always center the modal on screen
        styles={{
          header: {
            padding: '12px 24px',
            minHeight: 'auto'
          },
          body: {
            padding: '0 24px 24px 24px' // Remove top padding, we'll add it manually
          }
        }}
      >
        <div style={{ paddingTop: '12px' }}>
          <FormProvider {...methods}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{ flex: '1' }}>
              <FloatingSelectField
                name="appCode"
                floatingLabel="Ứng dụng"
                options={apps.map(app => ({
                  label: `${app.name} (${app.code})`,
                  value: app.code
                }))}
                showSearch
                formItemProps={{ style: { marginBottom: 0 } }}
              />
            </div>
            <div style={{ flex: '1' }}>
              <FloatingSelectField
                name="jobType"
                floatingLabel="Job Type"
                options={[
                  { label: 'Tất cả Job Types', value: 'ALL' },
                  { label: 'SENT', value: 'SENT' },
                  { label: 'SYNC_MESSAGE', value: 'SYNC_MESSAGE' },
                  { label: 'SYNC_TRACKING', value: 'SYNC_TRACKING' }
                ]}
                formItemProps={{ style: { marginBottom: 0 } }}
              />
            </div>
            <div style={{
              display: 'flex',
              gap: '8px',
              alignItems: 'center'
            }}>
              <Button
                type="primary"
                loading={loading}
                onClick={handleReset}
                style={{
                  backgroundColor: THEME_COLORS.BASE_COLOR,
                  borderColor: THEME_COLORS.BASE_COLOR,
                  minWidth: '70px'
                }}
              >
                Reset
              </Button>
              <Button
                onClick={() => {
                  setResetModalVisible(false);
                  methods.reset();
                }}
                style={{ minWidth: '70px' }}
              >
                Hủy
              </Button>
            </div>
          </div>
        </FormProvider>
        </div>
      </Modal>
    </div>
  );
};

export default AppManagement;