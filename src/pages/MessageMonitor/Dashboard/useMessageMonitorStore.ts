import { message } from 'antd';
import { TablePaginationConfig } from 'antd/es/table';
import { SorterResult } from 'antd/es/table/interface';
import { create } from 'zustand';

import { MessageMonitorApi, MessageMonitorItem, BulkResendReplayRequest } from '@/api/message-monitor';
import { BulkResendFormData } from '@/containers/MessageMonitor/MessageMonitorBulkResendModal';

// Define filter values type (kept for backward compatibility with bulk resend)
export interface FilterValues {
  searchText: string | null;
  producerId: string | null;
  topic: string | null;
  sentStatus: string | null;
  // Single date fields
  createdTime: Date | null;
  lastProcessedTime: Date | null;
}

// Define the shape of the store
interface MessageMonitorState {
  // Loading state (kept for modal loading)
  isFilterLoading: boolean;

  // Data state (kept for table when using external data)
  messageData: MessageMonitorItem[];
  selectedRowKeys: React.Key[];
  pagination: TablePaginationConfig;
  sorter: SorterResult<MessageMonitorItem> | null;

  // Modal state
  isDetailModalVisible: boolean;
  selectedRecordId: string | null;

  // Bulk resend modal state
  isBulkResendModalVisible: boolean;

  // Actions
  setIsFilterLoading: (loading: boolean) => void;
  setMessageData: (data: MessageMonitorItem[]) => void;
  setSelectedRowKeys: (keys: React.Key[]) => void;
  setPagination: (pagination: TablePaginationConfig) => void;
  setSorter: (sorter: SorterResult<MessageMonitorItem> | null) => void;
  setDetailModalVisible: (visible: boolean) => void;
  setSelectedRecordId: (id: string | null) => void;
  setBulkResendModalVisible: (visible: boolean) => void;

  // Table actions
  handleTableChange: (pagination: TablePaginationConfig, _: any, sorter: SorterResult<MessageMonitorItem> | SorterResult<MessageMonitorItem>[]) => void;
  handleMenuClick: (key: string, record: MessageMonitorItem) => void;

  // Resend actions
  handleResendMessage: (id: string) => Promise<void>;
  handleBulkResend: () => Promise<void>;
  handleBulkResendWithParams: (data: BulkResendFormData, messageIds?: string[]) => Promise<void>;
}

// Note: Filter helper functions removed as filter forms are now independent

export const useDashboardStore = create<MessageMonitorState>((set, get) => ({
  // Initial state
  isFilterLoading: false,
  messageData: [],
  selectedRowKeys: [],
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total) => `Tổng ${total} bản ghi`,
  },
  sorter: null,
  isDetailModalVisible: false,
  selectedRecordId: null,
  isBulkResendModalVisible: false,

  // Basic setters
  setIsFilterLoading: (loading) => set({ isFilterLoading: loading }),
  setMessageData: (data) => set({ messageData: data }),
  setSelectedRowKeys: (keys) => set({ selectedRowKeys: keys }),
  setPagination: (pagination) => set({ pagination }),
  setSorter: (sorter) => set({ sorter }),
  setDetailModalVisible: (visible) => set({ isDetailModalVisible: visible }),
  setSelectedRecordId: (id) => set({ selectedRecordId: id }),
  setBulkResendModalVisible: (visible) => set({ isBulkResendModalVisible: visible }),

  // Note: fetchData, handleSubmit, handleReset removed as filter forms are now independent

  // Handle table changes (pagination, sorting) - simplified for external data
  handleTableChange: (pagination, _, sorter) => {
    const normalizedSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    set({ pagination, sorter: normalizedSorter });
    // Note: External filter forms handle their own data fetching
  },

  // Handle menu actions
  handleMenuClick: (key, record) => {
    switch (key) {
      case 'view':
        set({ 
          isDetailModalVisible: true, 
          selectedRecordId: record.messageId 
        });
        break;
      case 'resend':
        get().handleResendMessage(record.messageId);
        break;
      default:
        break;
    }
  },

  // Handle single message resend using replay API
  handleResendMessage: async (id) => {
    try {
      // Prepare request data for replay API with single ID and other fields as null
      const requestData: BulkResendReplayRequest = {
        type: 'IDS',
        producerId: null,
        description: null,
        fromDate: null,
        toDate: null,
        messageIds: [id],
        scheduleAt: null,
      };

      const response = await MessageMonitorApi.bulkResendReplay(requestData);
      if (response.success) {
        message.success('Gửi lại bản tin thành công');
        // Note: External filter forms handle their own data refreshing
      } else {
        message.error(response.message || 'Có lỗi xảy ra khi gửi lại bản tin');
      }
    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi lại bản tin');
    }
  },



  // Handle bulk resend using replay API with selected IDs
  handleBulkResend: async () => {
    const { selectedRowKeys } = get();
    if (selectedRowKeys.length === 0) {
      // Show modal when no records are selected
      set({ isBulkResendModalVisible: true });
      return;
    }

    try {
      // Prepare request data for replay API with selected IDs and other fields as null
      const requestData: BulkResendReplayRequest = {
        type: 'IDS',
        producerId: null,
        description: null,
        fromDate: null,
        toDate: null,
        messageIds: selectedRowKeys as string[],
        scheduleAt: null,
      };

      const response = await MessageMonitorApi.bulkResendReplay(requestData);
      if (response.success) {
        message.success(`Gửi lại ${selectedRowKeys.length} bản tin thành công`);
        set({ selectedRowKeys: [] });
        // Note: External filter forms handle their own data refreshing
      } else {
        message.error(response.message || 'Có lỗi xảy ra khi gửi lại bản tin');
      }
    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi lại bản tin');
    }
  },



  // Handle bulk resend with parameters from modal
  handleBulkResendWithParams: async (data, messageIds) => {
    try {
      set({ isFilterLoading: true });

      // Prepare the request data
      const requestData: BulkResendReplayRequest = {
        type: data.type as 'RANGE' | 'IDS',
        producerId: data.producerId || null,
        description: data.description || null,
        fromDate: data.fromDate ? data.fromDate.toISOString() : null,
        toDate: data.toDate ? data.toDate.toISOString() : null,
        messageIds: data.type === 'IDS' ? (messageIds || []) : [],
        scheduleAt: data.scheduleAt ? data.scheduleAt.toISOString() : null,
      };

      const response = await MessageMonitorApi.bulkResendReplay(requestData);

      if (response.success) {
        message.success('Yêu cầu gửi lại bản tin đã được xử lý thành công!');
        // Close modal - external filter forms handle their own data refreshing
        set({ isBulkResendModalVisible: false });
      } else {
        message.error(response.message || 'Có lỗi xảy ra khi gửi lại bản tin');
      }

    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi lại bản tin');
    } finally {
      set({ isFilterLoading: false });
    }
  },
}));
