import { MessageOutlined, CheckCircleOutlined, ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { Card, Row, Col, Statistic, Table, message } from 'antd';
import React, { useState, useEffect, useMemo, useCallback } from 'react';

import { useDashboardStore } from './useMessageMonitorStore';

import { MessageMonitorApi } from '@/api/message-monitor';
import Status<PERSON>ie<PERSON><PERSON> from '@/components/StatusPieChart';
import { initializeApiToken } from '@/utils/common/storage';

interface ChartData {
  name: string;
  value: number;
  color: string;
}

const MessageDashboard: React.FC = () => {
  // Store state
  const {
    messageData,
    setMessageData,
    selectedRowKeys,
    setSelectedRowKeys,
    pagination,
    setPagination,
    sorter,
    handleTableChange,
    handleMenuClick,
    isDetailModalVisible,
    setDetailModalVisible,
    selectedRecordId,
  } = useDashboardStore();

  // Local state
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({
    total: 0,
    sent: 0,
    failed: 0,
    pending: 0,
  });

  // Initialize API token only once
  useEffect(() => {
    initializeApiToken();
  }, []); // Empty dependency array - run only once

  // Memoized chart data to prevent unnecessary recalculations
  const chartData = useMemo((): ChartData[] => [
    { name: 'Đã gửi', value: statistics.sent, color: '#52c41a' },
    { name: 'Thất bại', value: statistics.failed, color: '#ff4d4f' },
    { name: 'Đang chờ', value: statistics.pending, color: '#faad14' },
  ], [statistics.sent, statistics.failed, statistics.pending]);

  // Optimized data fetching function
  const fetchData = useCallback(async () => {
    if (loading) {return;} // Prevent multiple simultaneous calls
    
    try {
      setLoading(true);
      
      // Build query parameters
      const params = {
        page: pagination.current || 1,
        size: pagination.pageSize || 10,
        ...(sorter?.field && sorter?.order && {
          sortBy: sorter.field as string,
          sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
        }),
      };

      const response = await MessageMonitorApi.getAll(params);
      
      if (response.success && response.data) {
        const { content, totalElements } = response.data;
        
        // Update table data
        setMessageData(content || []);
        
        // Update pagination
        setPagination({
          ...pagination,
          total: totalElements || 0,
        });

        // Calculate statistics from the data
        const stats = {
          total: totalElements || 0,
          sent: content?.filter((item: any) => item.sentStatus === 1)?.length || 0,
          failed: content?.filter((item: any) => item.sentStatus === -1)?.length || 0,
          pending: content?.filter((item: any) => item.sentStatus === 0)?.length || 0,
        };
        
        setStatistics(stats);
      } else {
        message.error(response.message || 'Không thể tải dữ liệu');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error('Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  }, [pagination.current, pagination.pageSize, sorter, loading, setMessageData, setPagination]);

  // Fetch data when pagination or sorter changes
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Memoized table columns
  const columns = useMemo(() => [
    {
      title: 'Message ID',
      dataIndex: 'messageId',
      key: 'messageId',
      sorter: true,
      ellipsis: true,
    },
    {
      title: 'Producer ID',
      dataIndex: 'producerId',
      key: 'producerId',
      sorter: true,
    },
    {
      title: 'Topic',
      dataIndex: 'topic',
      key: 'topic',
      sorter: true,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'sentStatus',
      key: 'sentStatus',
      sorter: true,
      render: (status: string) => {
        const statusMap = {
          'SENT': { text: 'Đã gửi', color: 'green' },
          'FAILED': { text: 'Thất bại', color: 'red' },
          'PENDING': { text: 'Đang chờ', color: 'orange' },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
        return (
          <span style={{ color: statusInfo.color }}>
            {statusInfo.text}
          </span>
        );
      },
    },
    {
      title: 'Thời gian tạo',
      dataIndex: 'createdTime',
      key: 'createdTime',
      sorter: true,
      render: (time: string) => time ? new Date(time).toLocaleString('vi-VN') : '-',
    },
    {
      title: 'Thời gian xử lý',
      dataIndex: 'lastProcessedTime',
      key: 'lastProcessedTime',
      sorter: true,
      render: (time: string) => time ? new Date(time).toLocaleString('vi-VN') : '-',
    },
    {
      title: 'Thao tác',
      key: 'action',
      render: (_: any, record: any) => (
        <div>
          <a onClick={() => handleMenuClick('view', record)} style={{ marginRight: 8 }}>
            Xem chi tiết
          </a>
          <a onClick={() => handleMenuClick('resend', record)}>
            Gửi lại
          </a>
        </div>
      ),
    },
  ], [handleMenuClick]);

  // Memoized row selection
  const rowSelection = useMemo(() => ({
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  }), [selectedRowKeys, setSelectedRowKeys]);

  return (
    <div style={{ padding: '24px' }}>
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng số bản tin"
              value={statistics.total}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Đã gửi"
              value={statistics.sent}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Thất bại"
              value={statistics.failed}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Đang chờ"
              value={statistics.pending}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Chart */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <Card title="Biểu đồ trạng thái bản tin">
            <StatusPieChart />
          </Card>
        </Col>
        <Col span={12}>
          {/* Additional chart or content can be added here */}
        </Col>
      </Row>

      {/* Table */}
      <Card title="Danh sách bản tin">
        <Table
          columns={columns}
          dataSource={messageData}
          loading={loading}
          pagination={pagination}
          rowSelection={rowSelection}
          rowKey="messageId"
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default MessageDashboard;