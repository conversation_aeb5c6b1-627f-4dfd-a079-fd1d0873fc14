import { message } from 'antd';
import { TablePaginationConfig } from 'antd/es/table';
import type { SorterResult } from 'antd/es/table/interface';
import React from 'react';
import { create } from 'zustand';

import { KafkaMonitorItem } from '@/api/kafka-monitor';
import { KafkaMonitorApi, KafkaMonitorSearchParams } from '@/api/kafka-monitor';
import { FilterValues } from '@/containers/KafkaMonitor/KafkaMonitorFilterForm';

// Helper function to format date for API in dd/MM/yyyy HH:mm:ss format
const formatDateForAPI = (date: Date): string => {
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
};


// Define the shape of the store
interface KafkaMonitorState {
  // Filter state
  filterValues: FilterValues | null;
  isFilterLoading: boolean;

  // Data state
  kafkaMonitorData: KafkaMonitorItem[];
  selectedRowKeys: React.Key[];
  pagination: TablePaginationConfig;
  sorter: SorterResult<KafkaMonitorItem> | null;

  // Modal state
  isDetailModalVisible: boolean;
  selectedRecordId: string | null;

  // Simple setters
  setFilterValues: (values: FilterValues | null) => void;
  setIsFilterLoading: (isLoading: boolean) => void;
  setKafkaMonitorData: (data: KafkaMonitorItem[]) => void;
  setSelectedRowKeys: (keys: React.Key[]) => void;
  setPagination: (pagination: TablePaginationConfig) => void;
  setSorter: (sorter: SorterResult<KafkaMonitorItem> | null) => void;
  setDetailModalVisible: (visible: boolean) => void;
  setSelectedRecordId: (id: string | null) => void;

  // Combined actions
  fetchData: (filterValues?: FilterValues | null, paginationParams?: TablePaginationConfig, sorterParams?: SorterResult<KafkaMonitorItem> | null) => Promise<void>;
  handleSubmit: (data: FilterValues) => void;
  handleReset: () => void;
  // Chart refresh callback
  chartRefreshCallback?: () => void;
  setChartRefreshCallback: (callback: () => void) => void;
  handleTableChange: (pagination: TablePaginationConfig, _: any, sorter: SorterResult<KafkaMonitorItem> | SorterResult<KafkaMonitorItem>[]) => void;
  handleMenuClick: (key: string, record: KafkaMonitorItem) => void;
  handleResendMessage: (id: string) => Promise<void>;
  handleMarkAsProcessed: (id: string) => Promise<void>;
  handleBulkResend: () => Promise<void>;
  handleBulkMarkAsProcessed: () => Promise<void>;
}

export const useDashboardStore = create<KafkaMonitorState>((set, get) => ({
  // Initial state with default application selected
  filterValues: {
    maUngDung: "014", // Default to NVCB CS
  },
  isFilterLoading: false,
  kafkaMonitorData: [],
  selectedRowKeys: [],
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total) => `Tổng ${total} bản ghi`,
  },
  sorter: null,
  isDetailModalVisible: false,
  selectedRecordId: null,

  // Simple state setters
  setFilterValues: (values) => set({ filterValues: values }),
  setIsFilterLoading: (isLoading) => set({ isFilterLoading: isLoading }),
  setKafkaMonitorData: (data) => set({ kafkaMonitorData: data }),
  setSelectedRowKeys: (keys) => set({ selectedRowKeys: keys }),
  setPagination: (pagination) => set({ pagination: pagination }),
  setSorter: (sorter) => set({ sorter: sorter }),
  setDetailModalVisible: (visible) => set({ isDetailModalVisible: visible }),
  setSelectedRecordId: (id) => set({ selectedRecordId: id }),

  fetchData: async (filterValues, paginationParams, sorterParams) => {
    const state = get();
    try {
      set({ isFilterLoading: true });

      // Use provided values or fall back to current state
      const filtersToUse = filterValues === null ? null : (filterValues || state.filterValues);
      const paginationToUse = paginationParams || state.pagination;
      const sorterToUse = sorterParams || state.sorter;

      // Extract maUngDung for the API path parameter
      const maUngDung = filtersToUse?.maUngDung || "014"; // Default to NVCB CS if not provided

      // Convert filter and pagination to API params (excluding maUngDung since it's now a path parameter)
      const params: Omit<KafkaMonitorSearchParams, 'maUngDung'> = {
        // Filter params
        searchText: filtersToUse?.searchText,
        topic: filtersToUse?.topic,
        trangThai: filtersToUse?.trangThai,
        guiNhan: filtersToUse?.guiNhan,
        loaiYeuCau: filtersToUse?.loaiYeuCau,

        // Date params - format as "dd/MM/yyyy HH:mm:ss,dd/MM/yyyy HH:mm:ss"
        ngayTao: (filtersToUse?.ngayTaoFromDate && filtersToUse?.ngayTaoToDate)
          ? `${formatDateForAPI(filtersToUse.ngayTaoFromDate)},${formatDateForAPI(filtersToUse.ngayTaoToDate)}`
          : undefined,
        ngayNhanPhanHoi: (filtersToUse?.ngayNhanPhanHoiFromDate && filtersToUse?.ngayNhanPhanHoiToDate)
          ? `${formatDateForAPI(filtersToUse.ngayNhanPhanHoiFromDate)},${formatDateForAPI(filtersToUse.ngayNhanPhanHoiToDate)}`
          : undefined,

        // Pagination params
        page: (paginationToUse.current ?? 1) - 1, // Convert to 0-based for API
        size: paginationToUse.pageSize,
      };

      // Add sorting if available
      if (sorterToUse && sorterToUse.field && sorterToUse.order) {
        const sortField = Array.isArray(sorterToUse.field)
          ? sorterToUse.field.join('.')
          : String(sorterToUse.field);
        const sortOrder = sorterToUse.order === 'ascend' ? 'asc' : 'desc';
        params.sort = `${sortField},${sortOrder}`;
      }

      // Call the actual API with maUngDung as path parameter
      const response = await KafkaMonitorApi.getAll(maUngDung, params);

      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch data');
      }

      // Update state with response data
      set({
        kafkaMonitorData: response.data.content,
        pagination: {
          ...paginationToUse,
          total: response.data.totalElements,
        },
        filterValues: filtersToUse,
      });
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error('Có lỗi xảy ra khi tải dữ liệu.');
      set({ kafkaMonitorData: [] });
    } finally {
      set({ isFilterLoading: false });
    }
  },

  handleSubmit: (data) => {
    const state = get();
    set({
      filterValues: data,
      pagination: { ...state.pagination, current: 1 }, // Reset to first page on new search
    });
    state.fetchData(data, { ...state.pagination, current: 1 }, state.sorter);

    // Trigger chart refresh with new filter values
    if (state.chartRefreshCallback) {
      state.chartRefreshCallback();
    }
  },

  handleReset: () => {
    const state = get();
    set({
      filterValues: null,
      pagination: { ...state.pagination, current: 1 },
    });
    state.fetchData(null, { ...state.pagination, current: 1 }, null);

    // Trigger chart refresh with reset values
    if (state.chartRefreshCallback) {
      state.chartRefreshCallback();
    }
  },

  // Chart refresh callback management
  setChartRefreshCallback: (callback) => set({ chartRefreshCallback: callback }),

  handleTableChange: (pagination, _, sorter) => {
    const state = get();
    const filterValues = state.filterValues === null ? undefined : state.filterValues;

    // Handle single sorter or array of sorters
    const normalizedSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    set({ sorter: normalizedSorter });

    state.fetchData(filterValues, pagination, normalizedSorter).catch((error) => {
      console.error(error);
    });
  },

  handleMenuClick: (key: string, record: KafkaMonitorItem) => {
    if (key === 'view') {
      // Open the detail modal
      set({
        selectedRecordId: record.id,
        isDetailModalVisible: true
      });
    } else if (key === 'resend') {
      // Handle resend action
      get().handleResendMessage(record.id);
    }
  },

  handleResendMessage: async (id: string) => {
    try {
      set({ isFilterLoading: true });
      const state = get();
      const maUngDung = state.filterValues?.maUngDung || "014";

      const response = await KafkaMonitorApi.resend(maUngDung, [id]);

      if (!response.success) {
        throw new Error(response.message || 'Failed to resend message');
      }

      message.success('Yêu cầu gửi lại đã được xử lý thành công!');

      // Refresh data
      state.fetchData();

    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi lại tin nhắn.');
    } finally {
      set({ isFilterLoading: false });
    }
  },

  handleMarkAsProcessed: async (id: string) => {
    try {
      set({ isFilterLoading: true });
      const state = get();
      const maUngDung = state.filterValues?.maUngDung || "014";

      const response = await KafkaMonitorApi.markAsProcessed(maUngDung, [id]);

      if (!response.success) {
        throw new Error(response.message || 'Failed to mark as processed');
      }

      message.success('Đã đánh dấu tin nhắn là đã xử lý!');

      // Close modal if open
      set({ isDetailModalVisible: false });

      // Refresh data
      state.fetchData();

    } catch (error) {
      message.error('Có lỗi xảy ra khi đánh dấu tin nhắn đã xử lý.');
    } finally {
      set({ isFilterLoading: false });
    }
  },

  handleBulkResend: async () => {
    try {
      const state = get();
      const { selectedRowKeys, filterValues } = state;

      // If no rows selected, show message
      if (selectedRowKeys.length === 0) {
        message.error('Vui lòng chọn bản tin');
        return;
      }

      set({ isFilterLoading: true });

      const maUngDung = filterValues?.maUngDung || "014";
      const idsToResend = selectedRowKeys.map(key => String(key));

      const response = await KafkaMonitorApi.resend(maUngDung, idsToResend);

      if (!response.success) {
        throw new Error(response.message || 'Failed to resend messages');
      }

      message.success(`Đã gửi lại thành công ${idsToResend.length} tin nhắn!`);

      // Clear selected rows and refresh data
      set({ selectedRowKeys: [] });
      state.fetchData();

    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi lại tin nhắn.');
    } finally {
      set({ isFilterLoading: false });
    }
  },

  handleBulkMarkAsProcessed: async () => {
    try {
      const state = get();
      const { selectedRowKeys, filterValues } = state;

      // If no rows selected, show message
      if (selectedRowKeys.length === 0) {
        message.error('Vui lòng chọn bản tin');
        return;
      }

      set({ isFilterLoading: true });

      const maUngDung = filterValues?.maUngDung || "014";
      const idsToProcess = selectedRowKeys.map(key => String(key));

      const response = await KafkaMonitorApi.markAsProcessed(maUngDung, idsToProcess);

      if (!response.success) {
        throw new Error(response.message || 'Failed to mark messages as processed');
      }

      message.success(`Đã đánh dấu thành công ${idsToProcess.length} tin nhắn là đã xử lý!`);

      // Clear selected rows and refresh data
      set({ selectedRowKeys: [] });
      state.fetchData();

    } catch (error) {
      message.error('Có lỗi xảy ra khi đánh dấu tin nhắn đã xử lý.');
    } finally {
      set({ isFilterLoading: false });
    }
  },
}));
